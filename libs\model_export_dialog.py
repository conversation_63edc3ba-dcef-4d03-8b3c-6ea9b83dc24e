#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模型导出对话框模块

提供YOLO模型导出为其他格式（ONNX、TensorRT等）的用户界面
"""

import os
import sys
import time
import logging
import subprocess
import platform
from pathlib import Path
from typing import Dict, List, Optional, Tuple

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
except ImportError:
    from PyQt4.QtGui import *
    from PyQt4.QtCore import *

# 导入项目模块
from libs.stringBundle import StringBundle
from libs.settings import Settings
from libs.constants import *
from libs.ai_assistant.model_manager import ModelManager

# 导入YOLO相关库
try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False

# 设置日志
logger = logging.getLogger(__name__)


class ExportConfig:
    """导出配置类"""
    
    def __init__(self):
        self.model_path = ""
        self.export_format = "onnx"
        self.output_dir = ""
        self.output_name = ""
        
        # ONNX参数
        self.onnx_opset = 12
        self.onnx_dynamic = False
        self.onnx_simplify = True
        
        # TensorRT参数
        self.tensorrt_precision = "fp16"
        self.tensorrt_workspace = 4
        
        # 通用参数
        self.image_size = 640
        self.batch_size = 1
        self.device = "cpu"


class ModelExportThread(QThread):
    """模型导出线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, str)  # 进度更新
    log_message = pyqtSignal(str)            # 日志消息
    export_completed = pyqtSignal(bool, str) # 导出完成
    
    def __init__(self, config: ExportConfig):
        super().__init__()
        self.config = config
        self.is_cancelled = False
    
    def cancel(self):
        """取消导出"""
        self.is_cancelled = True
    
    def run(self):
        """执行导出"""
        try:
            self.log_message.emit("开始模型导出...")
            self.progress_updated.emit(10, "正在加载模型...")
            
            if not YOLO_AVAILABLE:
                raise Exception("ultralytics库未安装，无法进行模型导出")
            
            # 检查模型文件
            if not os.path.exists(self.config.model_path):
                raise Exception(f"模型文件不存在: {self.config.model_path}")
            
            # 加载模型
            self.log_message.emit(f"加载模型: {self.config.model_path}")
            model = YOLO(self.config.model_path)
            
            if self.is_cancelled:
                return
            
            self.progress_updated.emit(30, "正在配置导出参数...")
            
            # 准备导出参数
            export_kwargs = self._prepare_export_kwargs()
            
            if self.is_cancelled:
                return
            
            self.progress_updated.emit(50, f"正在导出为{self.config.export_format.upper()}格式...")
            
            # 执行导出
            self.log_message.emit(f"开始导出为{self.config.export_format.upper()}格式...")
            
            if self.config.export_format == "onnx":
                result = model.export(format="onnx", **export_kwargs)
            elif self.config.export_format == "tensorrt":
                result = model.export(format="engine", **export_kwargs)
            elif self.config.export_format == "coreml":
                result = model.export(format="coreml", **export_kwargs)
            elif self.config.export_format == "tflite":
                result = model.export(format="tflite", **export_kwargs)
            else:
                raise Exception(f"不支持的导出格式: {self.config.export_format}")
            
            if self.is_cancelled:
                return
            
            self.progress_updated.emit(90, "正在完成导出...")
            
            # 移动文件到指定目录（如果需要）
            if self.config.output_dir and self.config.output_name:
                self._move_exported_file(result)
            
            self.progress_updated.emit(100, "导出完成")
            self.log_message.emit(f"模型导出成功: {result}")

            # 准备成功消息，包含文件路径信息
            final_path = result
            if self.config.output_dir and self.config.output_name:
                # 如果移动了文件，使用移动后的路径
                file_ext = Path(result).suffix
                final_path = os.path.join(self.config.output_dir, f"{self.config.output_name}{file_ext}")

            success_msg = f"模型导出成功!\n\n导出文件: {final_path}"
            self.export_completed.emit(True, success_msg)
            
        except Exception as e:
            error_msg = f"模型导出失败: {str(e)}"
            self.log_message.emit(error_msg)
            self.export_completed.emit(False, error_msg)
    
    def _prepare_export_kwargs(self) -> Dict:
        """准备导出参数"""
        kwargs = {
            "imgsz": self.config.image_size,
            "device": self.config.device,
        }
        
        if self.config.export_format == "onnx":
            kwargs.update({
                "opset": self.config.onnx_opset,
                "dynamic": self.config.onnx_dynamic,
                "simplify": self.config.onnx_simplify,
            })
        elif self.config.export_format == "tensorrt":
            kwargs.update({
                "half": self.config.tensorrt_precision == "fp16",
                "workspace": self.config.tensorrt_workspace,
            })
        
        return kwargs
    
    def _move_exported_file(self, exported_path: str):
        """移动导出的文件到指定目录"""
        try:
            if not os.path.exists(exported_path):
                return
            
            # 创建目标目录
            os.makedirs(self.config.output_dir, exist_ok=True)
            
            # 构建目标文件路径
            file_ext = Path(exported_path).suffix
            target_path = os.path.join(self.config.output_dir, f"{self.config.output_name}{file_ext}")
            
            # 移动文件
            import shutil
            shutil.move(exported_path, target_path)
            self.log_message.emit(f"文件已移动到: {target_path}")
            
        except Exception as e:
            self.log_message.emit(f"移动文件失败: {str(e)}")


class ModelExportDialog(QDialog):
    """模型导出对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.string_bundle = StringBundle.get_bundle()
        self.get_str = lambda str_id: self.string_bundle.get_string(str_id)

        # 加载设置
        self.settings = Settings()
        self.settings.load()

        # 导出线程
        self.export_thread = None

        # 初始化模型管理器
        self.model_manager = ModelManager()
        self.model_manager.models_updated.connect(self.update_model_list)

        self.init_ui()
        self.setup_style()
        self.load_settings()

        # 扫描可用模型
        self.refresh_models()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(self.get_str('exportModelDialog'))
        self.setModal(True)
        self.resize(600, 500)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel(self.get_str('exportModelTitle'))
        title_label.setObjectName("titleLabel")
        main_layout.addWidget(title_label)
        
        # 模型选择区域
        model_group = self.create_model_selection_group()
        main_layout.addWidget(model_group)
        
        # 导出格式选择区域
        format_group = self.create_format_selection_group()
        main_layout.addWidget(format_group)
        
        # 参数配置区域
        params_group = self.create_parameters_group()
        main_layout.addWidget(params_group)
        
        # 输出设置区域
        output_group = self.create_output_group()
        main_layout.addWidget(output_group)
        
        # 进度区域（初始隐藏）
        progress_group = self.create_progress_group()
        main_layout.addWidget(progress_group)
        
        # 按钮区域
        button_layout = self.create_button_layout()
        main_layout.addLayout(button_layout)
        
        # 初始隐藏进度区域
        self.progress_group.setVisible(False)

    def create_model_selection_group(self):
        """创建模型选择区域"""
        group = QGroupBox(self.get_str('selectModel'))
        layout = QVBoxLayout(group)

        # 模型下拉框选择
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel(self.get_str('modelPath')))

        self.model_combo = QComboBox()
        self.model_combo.setMinimumHeight(32)
        self.model_combo.currentTextChanged.connect(self.on_model_changed)
        model_layout.addWidget(self.model_combo, 1)

        # 刷新按钮
        self.refresh_model_btn = QPushButton("🔄")
        self.refresh_model_btn.setToolTip(self.get_str('refreshModels'))
        self.refresh_model_btn.setMaximumWidth(40)
        self.refresh_model_btn.clicked.connect(self.refresh_models)
        model_layout.addWidget(self.refresh_model_btn)

        # 浏览按钮（备用）
        self.browse_model_btn = QPushButton(self.get_str('browse'))
        self.browse_model_btn.setMaximumWidth(80)
        self.browse_model_btn.clicked.connect(self.browse_model_file)
        model_layout.addWidget(self.browse_model_btn)

        layout.addLayout(model_layout)

        # 模型信息显示
        self.model_info_label = QLabel(self.get_str('noModelSelected'))
        self.model_info_label.setObjectName("infoLabel")
        self.model_info_label.setWordWrap(True)
        layout.addWidget(self.model_info_label)

        return group

    def create_format_selection_group(self):
        """创建导出格式选择区域"""
        group = QGroupBox(self.get_str('exportFormat'))
        layout = QVBoxLayout(group)

        # 格式选择
        format_layout = QHBoxLayout()
        self.format_combo = QComboBox()
        self.format_combo.addItems([
            "ONNX (.onnx)",
            "TensorRT (.engine)",
            "CoreML (.mlmodel)",
            "TensorFlow Lite (.tflite)"
        ])
        self.format_combo.currentTextChanged.connect(self.on_format_changed)

        format_layout.addWidget(QLabel(self.get_str('format')))
        format_layout.addWidget(self.format_combo, 1)
        layout.addLayout(format_layout)

        # 格式说明
        self.format_desc_label = QLabel(self.get_str('onnxDescription'))
        self.format_desc_label.setObjectName("descLabel")
        self.format_desc_label.setWordWrap(True)
        layout.addWidget(self.format_desc_label)

        return group

    def create_parameters_group(self):
        """创建参数配置区域"""
        group = QGroupBox(self.get_str('exportParameters'))
        layout = QVBoxLayout(group)

        # 创建堆叠窗口用于不同格式的参数
        self.params_stack = QStackedWidget()

        # ONNX参数页面
        onnx_widget = self.create_onnx_params_widget()
        self.params_stack.addWidget(onnx_widget)

        # TensorRT参数页面
        tensorrt_widget = self.create_tensorrt_params_widget()
        self.params_stack.addWidget(tensorrt_widget)

        # CoreML参数页面
        coreml_widget = self.create_coreml_params_widget()
        self.params_stack.addWidget(coreml_widget)

        # TensorFlow Lite参数页面
        tflite_widget = self.create_tflite_params_widget()
        self.params_stack.addWidget(tflite_widget)

        layout.addWidget(self.params_stack)

        # 通用参数
        common_layout = QHBoxLayout()

        # 图像尺寸
        common_layout.addWidget(QLabel(self.get_str('imageSize')))
        self.image_size_spin = QSpinBox()
        self.image_size_spin.setRange(320, 1280)
        self.image_size_spin.setValue(640)
        self.image_size_spin.setSingleStep(32)
        common_layout.addWidget(self.image_size_spin)

        # 设备选择
        common_layout.addWidget(QLabel(self.get_str('device')))
        self.device_combo = QComboBox()
        self.device_combo.addItems(["cpu", "cuda:0"])
        common_layout.addWidget(self.device_combo)

        common_layout.addStretch()
        layout.addLayout(common_layout)

        return group

    def create_onnx_params_widget(self):
        """创建ONNX参数配置窗口"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # Opset版本
        self.onnx_opset_spin = QSpinBox()
        self.onnx_opset_spin.setRange(9, 17)
        self.onnx_opset_spin.setValue(12)
        layout.addRow(self.get_str('onnxOpset'), self.onnx_opset_spin)

        # 动态batch
        self.onnx_dynamic_check = QCheckBox(self.get_str('onnxDynamic'))
        layout.addRow("", self.onnx_dynamic_check)

        # 简化模型
        self.onnx_simplify_check = QCheckBox(self.get_str('onnxSimplify'))
        self.onnx_simplify_check.setChecked(True)
        layout.addRow("", self.onnx_simplify_check)

        return widget

    def create_tensorrt_params_widget(self):
        """创建TensorRT参数配置窗口"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # 精度模式
        self.tensorrt_precision_combo = QComboBox()
        self.tensorrt_precision_combo.addItems(["fp16", "fp32"])
        layout.addRow(self.get_str('tensorrtPrecision'), self.tensorrt_precision_combo)

        # 工作空间大小
        self.tensorrt_workspace_spin = QSpinBox()
        self.tensorrt_workspace_spin.setRange(1, 16)
        self.tensorrt_workspace_spin.setValue(4)
        self.tensorrt_workspace_spin.setSuffix(" GB")
        layout.addRow(self.get_str('tensorrtWorkspace'), self.tensorrt_workspace_spin)

        return widget

    def create_coreml_params_widget(self):
        """创建CoreML参数配置窗口"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # CoreML特定参数可以在这里添加
        info_label = QLabel(self.get_str('coremlInfo'))
        info_label.setWordWrap(True)
        layout.addRow(info_label)

        return widget

    def create_tflite_params_widget(self):
        """创建TensorFlow Lite参数配置窗口"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # TensorFlow Lite特定参数可以在这里添加
        info_label = QLabel(self.get_str('tfliteInfo'))
        info_label.setWordWrap(True)
        layout.addRow(info_label)

        return widget

    def create_output_group(self):
        """创建输出设置区域"""
        group = QGroupBox(self.get_str('outputSettings'))
        layout = QVBoxLayout(group)

        # 输出目录
        dir_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setPlaceholderText(self.get_str('selectOutputDir'))
        self.browse_output_btn = QPushButton(self.get_str('browse'))
        self.browse_output_btn.clicked.connect(self.browse_output_dir)

        dir_layout.addWidget(QLabel(self.get_str('outputDir')))
        dir_layout.addWidget(self.output_dir_edit, 1)
        dir_layout.addWidget(self.browse_output_btn)
        layout.addLayout(dir_layout)

        # 输出文件名
        name_layout = QHBoxLayout()
        self.output_name_edit = QLineEdit()
        self.output_name_edit.setPlaceholderText(self.get_str('outputFileName'))

        name_layout.addWidget(QLabel(self.get_str('fileName')))
        name_layout.addWidget(self.output_name_edit, 1)
        layout.addLayout(name_layout)

        return group

    def create_progress_group(self):
        """创建进度显示区域"""
        self.progress_group = QGroupBox(self.get_str('exportProgress'))
        layout = QVBoxLayout(self.progress_group)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel(self.get_str('ready'))
        layout.addWidget(self.status_label)

        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)

        return self.progress_group

    def create_button_layout(self):
        """创建按钮布局"""
        layout = QHBoxLayout()
        layout.addStretch()

        # 导出按钮
        self.export_btn = QPushButton(self.get_str('startExport'))
        self.export_btn.setObjectName("primaryButton")
        self.export_btn.clicked.connect(self.start_export)
        layout.addWidget(self.export_btn)

        # 取消按钮
        self.cancel_btn = QPushButton(self.get_str('cancel'))
        self.cancel_btn.clicked.connect(self.cancel_export)
        layout.addWidget(self.cancel_btn)

        # 关闭按钮
        self.close_btn = QPushButton(self.get_str('close'))
        self.close_btn.clicked.connect(self.close)
        layout.addWidget(self.close_btn)

        return layout

    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #fafafa;
            }

            QGroupBox {
                font-weight: bold;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #1976d2;
                background-color: white;
            }

            #titleLabel {
                font-size: 18px;
                font-weight: bold;
                color: #1976d2;
                margin-bottom: 10px;
            }

            #infoLabel, #descLabel {
                color: #666;
                font-style: italic;
                margin: 5px 0;
            }

            #primaryButton {
                background-color: #1976d2;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }

            #primaryButton:hover {
                background-color: #1565c0;
            }

            #primaryButton:disabled {
                background-color: #ccc;
            }

            QPushButton {
                padding: 6px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }

            QPushButton:hover {
                background-color: #f5f5f5;
                border-color: #1976d2;
            }

            QLineEdit, QComboBox, QSpinBox {
                padding: 6px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }

            QLineEdit:focus, QComboBox:focus, QSpinBox:focus {
                border-color: #1976d2;
                outline: none;
            }

            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f9f9f9;
                font-family: monospace;
                font-size: 12px;
            }

            QProgressBar {
                border: 1px solid #ddd;
                border-radius: 4px;
                text-align: center;
                background-color: #f0f0f0;
            }

            QProgressBar::chunk {
                background-color: #4caf50;
                border-radius: 3px;
            }
        """)

    def load_settings(self):
        """加载设置"""
        # 设置默认导出目录
        default_export_dir = self.get_default_export_dir()
        last_output_dir = self.settings.get(SETTING_MODEL_EXPORT_DIR, default_export_dir)

        # 确保目录存在
        if not os.path.exists(last_output_dir):
            try:
                os.makedirs(last_output_dir, exist_ok=True)
            except:
                last_output_dir = default_export_dir
                try:
                    os.makedirs(last_output_dir, exist_ok=True)
                except:
                    last_output_dir = os.path.expanduser("~")

        self.output_dir_edit.setText(last_output_dir)

        # 检测可用设备
        self.detect_available_devices()

    def get_default_export_dir(self):
        """获取默认导出目录"""
        # 优先使用项目根目录下的exports文件夹
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        exports_dir = os.path.join(project_root, "exports", "models")

        # 如果项目目录不可写，使用用户文档目录
        try:
            os.makedirs(exports_dir, exist_ok=True)
            # 测试写入权限
            test_file = os.path.join(exports_dir, ".test")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            return exports_dir
        except:
            # 使用用户文档目录
            documents_dir = os.path.join(os.path.expanduser("~"), "Documents", "labelImg_exports", "models")
            try:
                os.makedirs(documents_dir, exist_ok=True)
                return documents_dir
            except:
                return os.path.expanduser("~")

    def detect_available_devices(self):
        """检测可用设备"""
        devices = ["cpu"]

        try:
            import torch
            if torch.cuda.is_available():
                for i in range(torch.cuda.device_count()):
                    devices.append(f"cuda:{i}")
        except ImportError:
            pass

        self.device_combo.clear()
        self.device_combo.addItems(devices)

    def refresh_models(self):
        """刷新模型列表"""
        try:
            if hasattr(self, 'model_manager'):
                models = self.model_manager.scan_models()
                if not models:
                    self.model_info_label.setText(self.get_str('noModelsFound'))
        except Exception as e:
            print(f"刷新模型失败: {e}")

    def update_model_list(self, models):
        """更新模型下拉列表"""
        try:
            self.model_combo.clear()

            if not models:
                self.model_combo.addItem(self.get_str('noModelsAvailable'))
                self.model_combo.setEnabled(False)
                return

            self.model_combo.setEnabled(True)

            # 分类模型
            official_models = ['yolov8n.pt', 'yolov8s.pt', 'yolov8m.pt', 'yolov8l.pt', 'yolov8x.pt',
                              'yolo11n.pt', 'yolo11s.pt', 'yolo11m.pt', 'yolo11l.pt', 'yolo11x.pt']
            training_models = []
            custom_models = []

            for model_path in models:
                model_name = os.path.basename(model_path)
                if model_name in official_models:
                    # 官方模型
                    display_name = f"📦 {model_name}"
                    self.model_combo.addItem(display_name, model_path)
                elif 'runs/train' in model_path.replace('\\', '/'):
                    training_models.append(model_path)
                else:
                    custom_models.append(model_path)

            # 添加训练结果模型
            for model_path in training_models:
                display_name = self._format_training_model_name(model_path)
                self.model_combo.addItem(display_name, model_path)

            # 添加自定义模型
            for model_path in custom_models:
                model_name = f"📄 {os.path.basename(model_path)}"
                self.model_combo.addItem(model_name, model_path)

            # 智能默认选择
            self._select_default_model()

        except Exception as e:
            print(f"更新模型列表失败: {e}")

    def _format_training_model_name(self, model_path):
        """格式化训练模型名称"""
        try:
            path_parts = model_path.replace('\\', '/').split('/')
            if 'runs' in path_parts and 'train' in path_parts:
                train_idx = path_parts.index('train')
                if train_idx + 1 < len(path_parts):
                    experiment_name = path_parts[train_idx + 1]
                    return f"🎯 {experiment_name}/best.pt"
            return f"🎯 {os.path.basename(model_path)}"
        except:
            return f"🎯 {os.path.basename(model_path)}"

    def _select_default_model(self):
        """智能选择默认模型"""
        try:
            # 优先选择推荐的模型
            default_models = ["yolov8s.pt", "yolov8n.pt", "best.pt"]

            for default_model in default_models:
                for i in range(self.model_combo.count()):
                    if default_model in self.model_combo.itemText(i):
                        self.model_combo.setCurrentIndex(i)
                        return

            # 如果没有找到默认模型，选择第一个
            if self.model_combo.count() > 0:
                self.model_combo.setCurrentIndex(0)

        except Exception as e:
            print(f"选择默认模型失败: {e}")

    def on_model_changed(self, model_text):
        """模型选择改变事件"""
        try:
            current_index = self.model_combo.currentIndex()
            if current_index >= 0:
                model_path = self.model_combo.itemData(current_index)
                if model_path:
                    self.update_model_info(model_path)
                else:
                    # 如果没有数据，可能是"无可用模型"等提示文本
                    self.model_info_label.setText(self.get_str('noModelSelected'))
        except Exception as e:
            print(f"模型选择改变处理失败: {e}")

    def get_selected_model_path(self):
        """获取当前选择的模型路径"""
        try:
            current_index = self.model_combo.currentIndex()
            if current_index >= 0:
                return self.model_combo.itemData(current_index)
            return None
        except:
            return None

    def browse_model_file(self):
        """浏览模型文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            self.get_str('selectModelFile'),
            "",
            "YOLO Models (*.pt *.onnx *.engine);;All Files (*)"
        )

        if file_path:
            # 添加到下拉框（如果不存在）
            display_name = f"📁 {os.path.basename(file_path)}"

            # 检查是否已存在
            found = False
            for i in range(self.model_combo.count()):
                if self.model_combo.itemData(i) == file_path:
                    self.model_combo.setCurrentIndex(i)
                    found = True
                    break

            if not found:
                self.model_combo.addItem(display_name, file_path)
                self.model_combo.setCurrentIndex(self.model_combo.count() - 1)

            self.update_model_info(file_path)

    def browse_output_dir(self):
        """浏览输出目录"""
        directory = QFileDialog.getExistingDirectory(
            self,
            self.get_str('selectOutputDir'),
            self.output_dir_edit.text() or os.path.expanduser("~")
        )

        if directory:
            self.output_dir_edit.setText(directory)
            # 保存到设置
            self.settings[SETTING_MODEL_EXPORT_DIR] = directory
            self.settings.save()

    def update_model_info(self, model_path):
        """更新模型信息显示"""
        try:
            if not model_path or not os.path.exists(model_path):
                self.model_info_label.setText(self.get_str('modelFileNotFound'))
                return

            # 获取文件信息
            file_size = os.path.getsize(model_path)
            size_mb = file_size / (1024 * 1024)
            file_name = os.path.basename(model_path)

            # 尝试获取模型详细信息
            model_type = self._detect_model_type(model_path)

            # 构建信息文本
            info_lines = [
                f"📄 {self.get_str('modelFile')}: {file_name}",
                f"📊 {self.get_str('fileSize')}: {size_mb:.1f} MB",
                f"🏷️ {self.get_str('modelType')}: {model_type}"
            ]

            # 如果是YOLO模型，尝试获取更多信息
            if model_path.endswith('.pt'):
                try:
                    if YOLO_AVAILABLE:
                        model_info = self._get_yolo_model_info(model_path)
                        if model_info:
                            info_lines.extend(model_info)
                except:
                    pass  # 忽略模型加载错误

            self.model_info_label.setText("\n".join(info_lines))

            # 自动设置输出文件名
            if not self.output_name_edit.text():
                base_name = os.path.splitext(file_name)[0]
                format_ext = self._get_format_extension()
                suggested_name = f"{base_name}_exported"
                self.output_name_edit.setText(suggested_name)

        except Exception as e:
            self.model_info_label.setText(f"{self.get_str('modelInfoError')}: {str(e)}")

    def _detect_model_type(self, model_path):
        """检测模型类型"""
        file_name = os.path.basename(model_path).lower()

        if 'yolov8' in file_name:
            return "YOLOv8"
        elif 'yolo11' in file_name or 'yolov11' in file_name:
            return "YOLOv11"
        elif 'yolo' in file_name:
            return "YOLO"
        elif file_name.endswith('.pt'):
            return "PyTorch"
        elif file_name.endswith('.onnx'):
            return "ONNX"
        elif file_name.endswith('.engine'):
            return "TensorRT"
        else:
            return self.get_str('unknown')

    def _get_yolo_model_info(self, model_path):
        """获取YOLO模型详细信息"""
        try:
            if not YOLO_AVAILABLE:
                return None

            # 快速加载模型获取基本信息
            model = YOLO(model_path)
            info_lines = []

            # 获取类别数量
            if hasattr(model, 'model') and hasattr(model.model, 'names'):
                class_count = len(model.model.names)
                info_lines.append(f"🎯 {self.get_str('classCount')}: {class_count}")
            elif hasattr(model, 'names'):
                class_count = len(model.names)
                info_lines.append(f"🎯 {self.get_str('classCount')}: {class_count}")

            # 清理模型对象
            del model

            return info_lines
        except:
            return None

    def _get_format_extension(self):
        """获取当前选择格式的扩展名"""
        format_text = self.format_combo.currentText()
        if "ONNX" in format_text:
            return ".onnx"
        elif "TensorRT" in format_text:
            return ".engine"
        elif "CoreML" in format_text:
            return ".mlmodel"
        elif "TensorFlow Lite" in format_text:
            return ".tflite"
        return ""

    def on_format_changed(self, format_text):
        """格式改变事件"""
        format_map = {
            "ONNX (.onnx)": (0, self.get_str('onnxDescription')),
            "TensorRT (.engine)": (1, self.get_str('tensorrtDescription')),
            "CoreML (.mlmodel)": (2, self.get_str('coremlDescription')),
            "TensorFlow Lite (.tflite)": (3, self.get_str('tfliteDescription'))
        }

        if format_text in format_map:
            index, description = format_map[format_text]
            self.params_stack.setCurrentIndex(index)
            self.format_desc_label.setText(description)

            # 更新输出文件名后缀
            if self.output_name_edit.text():
                current_name = self.output_name_edit.text()
                # 移除旧的后缀（如果有）
                base_name = current_name.split('_exported')[0] if '_exported' in current_name else current_name
                # 添加新的建议名称
                format_suffix = format_text.split('(')[1].split(')')[0].replace('.', '')
                new_name = f"{base_name}_exported_{format_suffix}"
                self.output_name_edit.setText(new_name)

    def validate_inputs(self):
        """验证输入"""
        # 检查模型文件
        model_path = self.get_selected_model_path()
        if not model_path:
            QMessageBox.warning(self, self.get_str('warning'), self.get_str('pleaseSelectModel'))
            return False

        if not os.path.exists(model_path):
            QMessageBox.warning(self, self.get_str('warning'), self.get_str('modelFileNotFound'))
            return False

        # 检查输出目录
        output_dir = self.output_dir_edit.text().strip()
        if output_dir and not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir, exist_ok=True)
            except Exception as e:
                QMessageBox.warning(self, self.get_str('warning'),
                                  f"{self.get_str('createDirFailed')}: {str(e)}")
                return False

        # 检查输出文件名
        output_name = self.output_name_edit.text().strip()
        if not output_name:
            QMessageBox.warning(self, self.get_str('warning'), self.get_str('pleaseEnterFileName'))
            return False

        return True

    def get_export_config(self):
        """获取导出配置"""
        config = ExportConfig()

        # 基本设置
        config.model_path = self.get_selected_model_path() or ""
        config.output_dir = self.output_dir_edit.text().strip()
        config.output_name = self.output_name_edit.text().strip()

        # 格式设置
        format_text = self.format_combo.currentText()
        if "ONNX" in format_text:
            config.export_format = "onnx"
        elif "TensorRT" in format_text:
            config.export_format = "tensorrt"
        elif "CoreML" in format_text:
            config.export_format = "coreml"
        elif "TensorFlow Lite" in format_text:
            config.export_format = "tflite"

        # 通用参数
        config.image_size = self.image_size_spin.value()
        config.device = self.device_combo.currentText()

        # 格式特定参数
        if config.export_format == "onnx":
            config.onnx_opset = self.onnx_opset_spin.value()
            config.onnx_dynamic = self.onnx_dynamic_check.isChecked()
            config.onnx_simplify = self.onnx_simplify_check.isChecked()
        elif config.export_format == "tensorrt":
            config.tensorrt_precision = self.tensorrt_precision_combo.currentText()
            config.tensorrt_workspace = self.tensorrt_workspace_spin.value()

        return config

    def start_export(self):
        """开始导出"""
        if not self.validate_inputs():
            return

        # 获取导出配置
        config = self.get_export_config()

        # 禁用控件
        self.export_btn.setEnabled(False)
        self.browse_model_btn.setEnabled(False)
        self.browse_output_btn.setEnabled(False)
        self.refresh_model_btn.setEnabled(False)
        self.model_combo.setEnabled(False)
        self.output_dir_edit.setEnabled(False)
        self.output_name_edit.setEnabled(False)
        self.format_combo.setEnabled(False)

        # 显示进度区域
        self.progress_group.setVisible(True)
        self.progress_bar.setValue(0)
        self.log_text.clear()
        self.status_label.setText(self.get_str('preparingExport'))

        # 创建并启动导出线程
        self.export_thread = ModelExportThread(config)
        self.export_thread.progress_updated.connect(self.on_progress_updated)
        self.export_thread.log_message.connect(self.on_log_message)
        self.export_thread.export_completed.connect(self.on_export_completed)
        self.export_thread.start()

    def cancel_export(self):
        """取消导出"""
        if self.export_thread and self.export_thread.isRunning():
            self.export_thread.cancel()
            self.export_thread.wait(3000)  # 等待3秒

            if self.export_thread.isRunning():
                self.export_thread.terminate()
                self.export_thread.wait()

            self.on_export_completed(False, self.get_str('exportCancelled'))
        else:
            self.close()

    def on_progress_updated(self, value, message):
        """进度更新"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)

    def on_log_message(self, message):
        """日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def on_export_completed(self, success, message):
        """导出完成"""
        # 恢复控件状态
        self.export_btn.setEnabled(True)
        self.browse_model_btn.setEnabled(True)
        self.browse_output_btn.setEnabled(True)
        self.refresh_model_btn.setEnabled(True)
        self.model_combo.setEnabled(True)
        self.output_dir_edit.setEnabled(True)
        self.output_name_edit.setEnabled(True)
        self.format_combo.setEnabled(True)

        if success:
            self.status_label.setText(self.get_str('exportComplete'))

            # 创建成功对话框，包含打开文件夹选项
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle(self.get_str('success'))
            msg_box.setText(self.get_str('exportSuccess'))
            msg_box.setDetailedText(message)
            msg_box.setIcon(QMessageBox.Information)

            # 添加按钮
            open_folder_btn = msg_box.addButton(self.get_str('openFolder'), QMessageBox.ActionRole)
            ok_btn = msg_box.addButton(self.get_str('ok'), QMessageBox.AcceptRole)

            msg_box.exec_()

            # 检查用户点击的按钮
            if msg_box.clickedButton() == open_folder_btn:
                self.open_export_folder()
        else:
            self.status_label.setText(self.get_str('exportFailed'))
            QMessageBox.critical(self, self.get_str('error'), message)

        # 清理线程
        if self.export_thread:
            self.export_thread.deleteLater()
            self.export_thread = None

    def open_export_folder(self):
        """打开导出文件夹"""
        try:
            export_dir = self.output_dir_edit.text().strip()
            if not export_dir or not os.path.exists(export_dir):
                QMessageBox.warning(self, self.get_str('warning'), self.get_str('folderNotFound'))
                return

            # 根据操作系统打开文件夹
            system = platform.system()
            if system == "Windows":
                os.startfile(export_dir)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", export_dir])
            else:  # Linux
                subprocess.run(["xdg-open", export_dir])

        except Exception as e:
            QMessageBox.warning(self, self.get_str('error'),
                              f"{self.get_str('openFolderFailed')}: {str(e)}")

    def closeEvent(self, event):
        """关闭事件"""
        if self.export_thread and self.export_thread.isRunning():
            reply = QMessageBox.question(
                self,
                self.get_str('confirmClose'),
                self.get_str('exportInProgress'),
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.export_thread.cancel()
                self.export_thread.wait(3000)
                if self.export_thread.isRunning():
                    self.export_thread.terminate()
                    self.export_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
